# train_idcard.py
"""
Train an ID card classifier using transfer learning (MobileNetV2).
Place your dataset in 'dataset/' with subfolders for each card type.
"""

# Gives Access to the file system
import os 

# Used to save the label mapping as a JSON file so you can read it later in the prediction script.
import json 

# Imports TensorFlow, the library used to build and train neural networks.
import tensorflow as tf 

# A Keras helper that loads images from disk and applies preprocessing and augmentation on the fly.
from tensorflow.keras.preprocessing.image import ImageDataGenerator

# Convenient shortcuts to create neural network layers, model objects, and training callbacks (tools that run during training).
from tensorflow.keras import layers, models, callbacks
